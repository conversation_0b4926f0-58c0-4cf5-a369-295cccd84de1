/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.87);
  background-color: #0f172a;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background: linear-gradient(to bottom, #0f172a, #1e293b, #0f172a);
}

#app {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 主容器样式 */
.dashboard-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(to bottom, #0f172a, #1e293b, #0f172a);
  color: white;
}

.dashboard-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* 头部样式 */
.dashboard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.dashboard-title {
  font-size: 2rem;
  font-weight: 600;
  margin: 0;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.status-dot.connected {
  background-color: #10b981;
}

.status-dot.connecting {
  background-color: #f59e0b;
}

.status-dot.disconnected {
  background-color: #ef4444;
}

.status-text {
  opacity: 0.8;
}

/* 视频容器样式 - 居中显示 */
.video-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 16px;
}

.video-wrapper {
  max-width: 1024px;
  width: 100%;
  position: relative;
  z-index: 0;
}

.video-frame {
  border-radius: 16px;
  overflow: hidden;
  background: linear-gradient(to bottom, #0f172a, #000000);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.6);
}

.video-content {
  background-color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: contain;
  pointer-events: none;
}

/* 底部工具栏样式 */
.toolbar-container {
  padding: 0 16px 16px;
  z-index: 20;
}

.toolbar {
  max-width: 1024px;
  margin: 0 auto;
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(24px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
  pointer-events: auto;
}

.toolbar-content {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  font-size: 0.875rem;
}

/* 工具栏按钮组 */
.toolbar-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-center {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 按钮样式 */
.btn {
  padding: 8px 12px;
  border-radius: 8px;
  border: none;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.btn-primary {
  background-color: #6366f1;
  color: white;
}

.btn-primary:hover {
  background-color: #4f46e5;
}

.btn-danger {
  background-color: #dc2626;
  color: white;
}

.btn-danger:hover {
  background-color: #b91c1c;
}

.btn-secondary {
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: transparent;
  color: white;
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 麦克风按钮 */
.mic-button {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  font-size: 1.25rem;
}

.mic-button.recording {
  background-color: #dc2626;
  animation: pulse 2s infinite;
}

.mic-button.idle {
  background-color: #6366f1;
}

.mic-button.idle:hover {
  background-color: #4f46e5;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 侧边栏样式 */
.sidebar-overlay {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
}

.sidebar-backdrop {
  flex: 1;
}

.sidebar {
  width: 100%;
  max-width: 448px;
  height: 100%;
  background: rgba(15, 23, 42, 0.9);
  backdrop-filter: blur(24px);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: -10px 0 30px rgba(0, 0, 0, 0.5);
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-title {
  font-weight: 500;
}

.sidebar-content {
  padding: 16px;
}

/* 侧边栏内容样式 */
.tab-container {
  margin-bottom: 12px;
  display: flex;
  gap: 12px;
}

.tab-button {
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: transparent;
  color: white;
  cursor: pointer;
  transition: all 0.2s;
}

.tab-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.tab-button.active {
  background-color: #6366f1;
  color: white;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.chat-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.chat-box {
  height: 320px;
  overflow-y: auto;
  padding: 12px;
  border-radius: 8px;
  background: rgba(15, 23, 42, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.chat-message {
  padding: 8px;
  border-radius: 6px;
  border-left: 4px solid;
}

.chat-message.system {
  border-left-color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}

.chat-message.user {
  border-left-color: #0ea5e9;
  background: rgba(14, 165, 233, 0.1);
}

.chat-message.assistant {
  border-left-color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}

.chat-input-container {
  display: flex;
  gap: 8px;
}

.chat-textarea {
  flex: 1;
  padding: 8px;
  border-radius: 6px;
  background: rgba(15, 23, 42, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  resize: vertical;
  min-height: 80px;
}

.chat-textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.chat-hint {
  text-align: center;
  font-size: 0.75rem;
  opacity: 0.7;
}

.tts-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tts-label {
  font-size: 0.875rem;
  opacity: 0.8;
}

.tts-textarea {
  width: 100%;
  padding: 8px;
  border-radius: 6px;
  background: rgba(15, 23, 42, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  resize: vertical;
  min-height: 200px;
}

.tts-textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* 设置页面样式 */
.settings-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.setting-label {
  font-size: 0.875rem;
  opacity: 0.8;
}

.range-input {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.1);
  outline: none;
  cursor: pointer;
}

.range-input::-webkit-slider-thumb {
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #6366f1;
  cursor: pointer;
}

.range-input::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #6366f1;
  cursor: pointer;
  border: none;
}

.checkbox-label {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkbox-input {
  accent-color: #6366f1;
}

/* 版权信息 */
.footer {
  text-align: center;
  color: #94a3b8;
  font-size: 0.875rem;
  padding: 16px 0;
}
