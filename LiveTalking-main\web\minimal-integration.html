<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>数字人集成示例</title>
    <style>
        .digital-human-container {
            width: 400px;
            height: 300px;
            border: 1px solid #ccc;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
        }
        
        .digital-human-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .controls {
            margin-top: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        
        .chat-input {
            width: 100%;
            padding: 10px;
            margin-top: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="digital-human-container">
        <video id="digital-human-video" class="digital-human-video" autoplay playsinline></video>
        <audio id="digital-human-audio" autoplay></audio>
    </div>
    
    <div class="controls">
        <button id="connect-btn" class="btn btn-primary">连接数字人</button>
        <button id="disconnect-btn" class="btn btn-danger" style="display: none;">断开连接</button>
    </div>
    
    <div>
        <input type="text" id="chat-input" class="chat-input" placeholder="输入要对数字人说的话...">
        <button id="send-btn" class="btn btn-primary">发送</button>
    </div>
    
    <input type="hidden" id="sessionid" value="0">

    <script>
        class DigitalHumanClient {
            constructor(serverUrl = 'http://************:8010') {
                this.serverUrl = serverUrl;
                this.pc = null;
                this.sessionId = 0;
                this.isConnected = false;
                
                this.initEventListeners();
            }
            
            initEventListeners() {
                document.getElementById('connect-btn').addEventListener('click', () => this.connect());
                document.getElementById('disconnect-btn').addEventListener('click', () => this.disconnect());
                document.getElementById('send-btn').addEventListener('click', () => this.sendMessage());
                document.getElementById('chat-input').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') this.sendMessage();
                });
            }
            
            async connect() {
                try {
                    const config = {
                        sdpSemantics: 'unified-plan',
                        iceServers: [{ urls: ['stun:stun.l.google.com:19302'] }]
                    };
                    
                    this.pc = new RTCPeerConnection(config);
                    
                    // 处理接收到的音视频流
                    this.pc.addEventListener('track', (evt) => {
                        if (evt.track.kind === 'video') {
                            document.getElementById('digital-human-video').srcObject = evt.streams[0];
                        } else if (evt.track.kind === 'audio') {
                            document.getElementById('digital-human-audio').srcObject = evt.streams[0];
                        }
                    });
                    
                    // 添加接收器
                    this.pc.addTransceiver('video', { direction: 'recvonly' });
                    this.pc.addTransceiver('audio', { direction: 'recvonly' });
                    
                    // 创建offer
                    const offer = await this.pc.createOffer();
                    await this.pc.setLocalDescription(offer);
                    
                    // 等待ICE收集完成
                    await this.waitForIceGathering();
                    
                    // 发送offer到服务器
                    const response = await fetch(`${this.serverUrl}/offer`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            sdp: this.pc.localDescription.sdp,
                            type: this.pc.localDescription.type
                        })
                    });
                    
                    const answer = await response.json();
                    this.sessionId = answer.sessionid;
                    document.getElementById('sessionid').value = this.sessionId;
                    
                    // 设置远程描述
                    await this.pc.setRemoteDescription(answer);
                    
                    this.isConnected = true;
                    this.updateUI();
                    
                    console.log('数字人连接成功，会话ID:', this.sessionId);
                    
                } catch (error) {
                    console.error('连接失败:', error);
                    alert('连接失败: ' + error.message);
                }
            }
            
            waitForIceGathering() {
                return new Promise((resolve) => {
                    if (this.pc.iceGatheringState === 'complete') {
                        resolve();
                    } else {
                        const checkState = () => {
                            if (this.pc.iceGatheringState === 'complete') {
                                this.pc.removeEventListener('icegatheringstatechange', checkState);
                                resolve();
                            }
                        };
                        this.pc.addEventListener('icegatheringstatechange', checkState);
                    }
                });
            }
            
            disconnect() {
                if (this.pc) {
                    this.pc.close();
                    this.pc = null;
                }
                
                this.isConnected = false;
                this.sessionId = 0;
                this.updateUI();
                
                // 清空视频
                document.getElementById('digital-human-video').srcObject = null;
                document.getElementById('digital-human-audio').srcObject = null;
                
                console.log('数字人连接已断开');
            }
            
            async sendMessage() {
                if (!this.isConnected) {
                    alert('请先连接数字人');
                    return;
                }
                
                const input = document.getElementById('chat-input');
                const message = input.value.trim();
                
                if (!message) return;
                
                try {
                    await fetch(`${this.serverUrl}/human`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            text: message,
                            type: 'chat',  // 或 'echo' 用于朗读模式
                            interrupt: true,
                            sessionid: this.sessionId
                        })
                    });
                    
                    input.value = '';
                    console.log('消息发送成功:', message);
                    
                } catch (error) {
                    console.error('发送消息失败:', error);
                    alert('发送失败: ' + error.message);
                }
            }
            
            updateUI() {
                const connectBtn = document.getElementById('connect-btn');
                const disconnectBtn = document.getElementById('disconnect-btn');
                
                if (this.isConnected) {
                    connectBtn.style.display = 'none';
                    disconnectBtn.style.display = 'inline-block';
                } else {
                    connectBtn.style.display = 'inline-block';
                    disconnectBtn.style.display = 'none';
                }
            }
        }
        
        // 初始化数字人客户端
        const digitalHuman = new DigitalHumanClient();
        
        // 页面卸载时断开连接
        window.addEventListener('beforeunload', () => {
            digitalHuman.disconnect();
        });
    </script>
</body>
</html>
