import logging
import sys
import io

# 配置日志器
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 文件处理器 - 使用UTF-8编码
fhandler = logging.FileHandler('livetalking.log', encoding='utf-8')
fhandler.setFormatter(formatter)
fhandler.setLevel(logging.INFO)
logger.addHandler(fhandler)

# 控制台处理器 - 使用UTF-8编码
try:
    # 尝试重新配置stdout为UTF-8
    if sys.platform == 'win32':
        # Windows下设置控制台为UTF-8
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
        sys.stderr.reconfigure(encoding='utf-8', errors='replace')

    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(logging.DEBUG)
    sformatter = logging.Formatter('%(levelname)s:%(name)s:%(message)s')
    handler.setFormatter(sformatter)
    logger.addHandler(handler)
except Exception as e:
    # 如果重新配置失败，使用安全的处理器
    class SafeStreamHandler(logging.StreamHandler):
        def emit(self, record):
            try:
                msg = self.format(record)
                # 移除或替换不支持的Unicode字符
                safe_msg = msg.encode('gbk', errors='replace').decode('gbk')
                self.stream.write(safe_msg + self.terminator)
                self.flush()
            except Exception:
                self.handleError(record)

    handler = SafeStreamHandler(sys.stdout)
    handler.setLevel(logging.DEBUG)
    sformatter = logging.Formatter('%(levelname)s:%(name)s:%(message)s')
    handler.setFormatter(sformatter)
    logger.addHandler(handler)