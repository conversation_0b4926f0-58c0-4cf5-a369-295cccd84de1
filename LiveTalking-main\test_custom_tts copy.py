#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自定义TTS接口的脚本
"""

import sys
import os
import argparse
import time
import numpy as np
from io import BytesIO

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ttsreal import CustomTTS
from logger import logger

class MockParent:
    """模拟父类，用于测试"""
    def __init__(self):
        self.sessionid = "test_session"
        self.audio_frames = []
    
    def put_audio_frame(self, frame, eventpoint=None):
        """接收音频帧"""
        self.audio_frames.append((frame, eventpoint))
        if eventpoint:
            logger.info(f"收到音频帧事件: {eventpoint}")

class MockOpt:
    """模拟配置选项"""
    def __init__(self, tts_server):
        self.fps = 50
        self.TTS_SERVER = tts_server
        self.REF_FILE = "Keira"  # 这个参数在CustomTTS中没有使用，但保持一致性
        self.REF_TEXT = None

def test_custom_tts(server_url, test_text):
    """测试自定义TTS接口"""
    logger.info(f"开始测试自定义TTS接口: {server_url}")
    logger.info(f"测试文本: {test_text}")
    
    # 创建模拟对象
    parent = MockParent()
    opt = MockOpt(server_url)
    
    # 创建CustomTTS实例
    tts = CustomTTS(opt, parent)
    
    # 测试文本转语音
    start_time = time.time()
    try:
        tts.txt_to_audio((test_text, None))
        end_time = time.time()
        
        print(f"TTS处理完成，耗时: {end_time - start_time:.2f}秒")
        print(f"生成的音频帧数量: {len(parent.audio_frames)}")
        
        # 检查是否有音频数据
        if parent.audio_frames:
            total_samples = sum(len(frame[0]) for frame in parent.audio_frames)
            duration = total_samples / 16000  # 16kHz采样率
            logger.info(f"音频总时长: {duration:.2f}秒")
            logger.info("测试成功！")
            return True
        else:
            logger.error("没有生成音频数据")
            return False
            
    except Exception as e:
        logger.exception(f"测试失败: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='测试自定义TTS接口')
    parser.add_argument('--server', type=str, default='http://*************:9873', 
                       help='TTS服务器地址')
    parser.add_argument('--text', type=str, default='Live2D是一种能让2D角色以3D方式动起来的技术', 
                       help='测试文本')
    
    args = parser.parse_args()
    
    # 运行测试
    success = test_custom_tts(args.server, args.text)
    
    if success:
        print("✅ 自定义TTS接口测试成功！")
        sys.exit(0)
    else:
        print("❌ 自定义TTS接口测试失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
