#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试avatar切换功能的脚本
"""

import requests
import json
import time
import sys

# 服务器配置
SERVER_HOST = "localhost"
SERVER_PORT = 8010
BASE_URL = f"http://{SERVER_HOST}:{SERVER_PORT}"

def test_get_avatar_list():
    """测试获取avatar列表"""
    print("=== 测试获取avatar列表 ===")
    try:
        response = requests.get(f"{BASE_URL}/avatar_list")
        if response.status_code == 200:
            data = response.json()
            if data["code"] == 0:
                print("✓ 成功获取avatar列表:")
                for avatar in data["data"]:
                    print(f"  - {avatar['avatar_id']}: {avatar['name']}")
                return data["data"]
            else:
                print(f"✗ 获取avatar列表失败: {data['msg']}")
                return []
        else:
            print(f"✗ HTTP请求失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"✗ 请求异常: {e}")
        return []

def test_switch_avatar(sessionid, avatar_id):
    """测试切换avatar"""
    print(f"=== 测试切换avatar到 {avatar_id} ===")
    try:
        payload = {
            "sessionid": sessionid,
            "avatar_id": avatar_id
        }
        response = requests.post(
            f"{BASE_URL}/switch_avatar",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data["code"] == 0:
                print(f"✓ 成功切换到avatar: {data['avatar_id']}")
                return True
            else:
                print(f"✗ 切换avatar失败: {data['msg']}")
                return False
        else:
            print(f"✗ HTTP请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 请求异常: {e}")
        return False

def test_create_session():
    """测试创建会话（模拟WebRTC offer）"""
    print("=== 测试创建会话 ===")
    try:
        # 这里使用一个简单的SDP offer进行测试
        # 实际使用中应该是真实的WebRTC SDP
        fake_offer = {
            "sdp": "v=0\r\no=- 0 0 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n",
            "type": "offer"
        }
        
        response = requests.post(
            f"{BASE_URL}/offer",
            json=fake_offer,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            if "sessionid" in data:
                sessionid = data["sessionid"]
                print(f"✓ 成功创建会话，sessionid: {sessionid}")
                return sessionid
            else:
                print("✗ 响应中没有sessionid")
                return None
        else:
            print(f"✗ HTTP请求失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"✗ 请求异常: {e}")
        return None

def test_is_speaking(sessionid):
    """测试检查是否在说话"""
    print("=== 测试检查说话状态 ===")
    try:
        payload = {"sessionid": sessionid}
        response = requests.post(
            f"{BASE_URL}/is_speaking",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data["code"] == 0:
                speaking = data["data"]
                print(f"✓ 说话状态: {'正在说话' if speaking else '未在说话'}")
                return speaking
            else:
                print(f"✗ 获取说话状态失败: {data.get('msg', '未知错误')}")
                return None
        else:
            print(f"✗ HTTP请求失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"✗ 请求异常: {e}")
        return None

def main():
    """主测试函数"""
    print("开始测试avatar切换功能...")
    print(f"服务器地址: {BASE_URL}")
    print("-" * 50)
    
    # 1. 测试获取avatar列表
    avatar_list = test_get_avatar_list()
    if not avatar_list:
        print("无法获取avatar列表，测试终止")
        return
    
    print("-" * 50)
    
    # 2. 测试创建会话（注意：这可能不会成功，因为需要真实的WebRTC环境）
    sessionid = test_create_session()
    if sessionid is None:
        print("无法创建会话，使用默认sessionid=0进行测试")
        sessionid = 0
    
    print("-" * 50)
    
    # 3. 测试检查说话状态
    test_is_speaking(sessionid)
    
    print("-" * 50)
    
    # 4. 测试切换avatar
    if len(avatar_list) >= 2:
        # 切换到第一个avatar
        first_avatar = avatar_list[0]["avatar_id"]
        test_switch_avatar(sessionid, first_avatar)
        
        time.sleep(1)
        
        # 切换到第二个avatar
        second_avatar = avatar_list[1]["avatar_id"]
        test_switch_avatar(sessionid, second_avatar)
        
        time.sleep(1)
        
        # 切换回第一个avatar
        test_switch_avatar(sessionid, first_avatar)
    else:
        print("可用avatar数量不足，无法测试切换功能")
    
    print("-" * 50)
    print("测试完成！")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "--help":
            print("用法: python test_avatar_switch.py [server_port]")
            print("默认端口: 8010")
            sys.exit(0)
        else:
            try:
                SERVER_PORT = int(sys.argv[1])
                BASE_URL = f"http://{SERVER_HOST}:{SERVER_PORT}"
            except ValueError:
                print("错误: 端口号必须是数字")
                sys.exit(1)
    
    main()
