# Vue2 数字人组件集成指南

## 快速开始

### 1. 安装和导入

将 `DigitalHuman.vue` 组件文件复制到你的 Vue2 项目中，然后在需要使用的地方导入：

```javascript
// 在单个组件中使用
import DigitalHuman from '@/components/DigitalHuman.vue'

export default {
  components: {
    DigitalHuman
  },
  // ...
}
```

或者全局注册：

```javascript
// main.js
import Vue from 'vue'
import DigitalHuman from '@/components/DigitalHuman.vue'

Vue.component('DigitalHuman', DigitalHuman)
```

### 2. 基础使用

```vue
<template>
  <div>
    <digital-human
      ref="digitalHuman"
      :server-url="'http://************:8010'"
      :width="400"
      :height="300"
      @connected="onConnected"
      @disconnected="onDisconnected"
      @message-sent="onMessageSent"
      @error="onError"
    />
  </div>
</template>

<script>
export default {
  methods: {
    onConnected(data) {
      console.log('数字人连接成功', data.sessionId)
    },
    onDisconnected() {
      console.log('数字人连接断开')
    },
    onMessageSent(data) {
      console.log('消息发送成功', data.text, data.type)
    },
    onError(error) {
      console.error('发生错误', error)
    },
    
    // 通过 ref 调用组件方法
    sendMessage() {
      this.$refs.digitalHuman.sendText('你好', 'chat')
    }
  }
}
</script>
```

## 组件 API

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `server-url` | String | `'http://************:8010'` | 数字人服务器地址 |
| `show-controls` | Boolean | `true` | 是否显示控制面板 |
| `width` | String/Number | `400` | 容器宽度 |
| `height` | String/Number | `300` | 容器高度 |
| `auto-connect` | Boolean | `false` | 是否自动连接 |
| `default-message-type` | String | `'chat'` | 默认消息类型：'chat' 或 'echo' |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `connecting` | - | 开始连接时触发 |
| `connected` | `{ sessionId }` | 连接成功时触发 |
| `disconnected` | - | 断开连接时触发 |
| `message-sent` | `{ text, type }` | 消息发送成功时触发 |
| `error` | `errorMessage` | 发生错误时触发 |

### Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `connect()` | - | `Promise` | 手动连接数字人 |
| `disconnect()` | - | - | 手动断开连接 |
| `sendText(text, type)` | `text: String, type: String` | `Promise` | 发送指定文本 |

## 使用场景示例

### 1. 客服助手集成

```vue
<template>
  <div class="customer-service">
    <digital-human
      ref="assistant"
      :server-url="serverUrl"
      :show-controls="false"
      :auto-connect="true"
      :width="300"
      :height="400"
      @connected="onAssistantReady"
      @error="onAssistantError"
    />
    
    <div class="chat-interface">
      <div class="messages" ref="messages">
        <div 
          v-for="msg in messages" 
          :key="msg.id"
          :class="['message', msg.type]"
        >
          {{ msg.text }}
        </div>
      </div>
      
      <div class="input-area">
        <input 
          v-model="userInput"
          @keyup.enter="sendUserMessage"
          placeholder="输入您的问题..."
        />
        <button @click="sendUserMessage">发送</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      serverUrl: 'http://************:8010',
      userInput: '',
      messages: [],
      messageId: 0
    }
  },
  methods: {
    onAssistantReady() {
      this.addMessage('助手已就绪，请问有什么可以帮您的？', 'assistant')
      // 发送欢迎语
      this.$refs.assistant.sendText('您好，我是智能客服助手，请问有什么可以帮您的？', 'echo')
    },
    
    onAssistantError(error) {
      this.addMessage('助手连接失败，请稍后重试', 'system')
    },
    
    sendUserMessage() {
      if (!this.userInput.trim()) return
      
      const userMessage = this.userInput.trim()
      this.addMessage(userMessage, 'user')
      
      // 发送给数字人处理
      this.$refs.assistant.sendText(userMessage, 'chat')
        .then(() => {
          this.addMessage('正在为您处理...', 'assistant')
        })
        .catch(error => {
          this.addMessage('发送失败，请重试', 'system')
        })
      
      this.userInput = ''
    },
    
    addMessage(text, type) {
      this.messages.push({
        id: this.messageId++,
        text,
        type,
        timestamp: new Date()
      })
      
      this.$nextTick(() => {
        this.$refs.messages.scrollTop = this.$refs.messages.scrollHeight
      })
    }
  }
}
</script>
```

### 2. 教育场景集成

```vue
<template>
  <div class="education-demo">
    <digital-human
      ref="teacher"
      :server-url="serverUrl"
      :show-controls="false"
      :auto-connect="true"
      default-message-type="echo"
      @connected="onTeacherReady"
    />
    
    <div class="lesson-controls">
      <h3>课程内容</h3>
      <div class="lesson-buttons">
        <button 
          v-for="lesson in lessons"
          :key="lesson.id"
          @click="startLesson(lesson)"
          class="lesson-btn"
        >
          {{ lesson.title }}
        </button>
      </div>
      
      <div class="current-lesson" v-if="currentLesson">
        <h4>{{ currentLesson.title }}</h4>
        <p>{{ currentLesson.description }}</p>
        <button @click="speakLessonContent">开始讲解</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      serverUrl: 'http://************:8010',
      currentLesson: null,
      lessons: [
        {
          id: 1,
          title: '数学基础',
          description: '学习基本的数学概念',
          content: '今天我们来学习数学基础知识。数学是一门研究数量、结构、变化以及空间等概念的学科。'
        },
        {
          id: 2,
          title: '英语语法',
          description: '掌握英语语法规则',
          content: '英语语法是英语语言的规则体系。今天我们学习基本的语法结构和时态。'
        }
      ]
    }
  },
  methods: {
    onTeacherReady() {
      this.$refs.teacher.sendText('同学们好，我是你们的AI老师，今天我们开始上课吧！', 'echo')
    },
    
    startLesson(lesson) {
      this.currentLesson = lesson
    },
    
    speakLessonContent() {
      if (this.currentLesson) {
        this.$refs.teacher.sendText(this.currentLesson.content, 'echo')
      }
    }
  }
}
</script>
```

## 高级配置

### 1. 自定义样式

组件使用了 scoped 样式，如果需要自定义外观，可以通过以下方式：

```vue
<template>
  <div class="custom-digital-human">
    <digital-human
      :width="'100%'"
      :height="500"
      class="my-digital-human"
    />
  </div>
</template>

<style>
.custom-digital-human {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

/* 使用深度选择器修改内部样式 */
.my-digital-human >>> .digital-human-container {
  border: 2px solid #007bff;
}

.my-digital-human >>> .status-indicator {
  background-color: rgba(0, 123, 255, 0.8);
}
</style>
```

### 2. 错误处理和重连

```javascript
export default {
  data() {
    return {
      reconnectAttempts: 0,
      maxReconnectAttempts: 3
    }
  },
  methods: {
    onError(error) {
      console.error('数字人错误:', error)
      
      // 自动重连逻辑
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.reconnectAttempts++
        setTimeout(() => {
          this.$refs.digitalHuman.connect()
        }, 2000 * this.reconnectAttempts) // 递增延迟
      } else {
        this.$message.error('数字人连接失败，请检查网络或联系技术支持')
      }
    },
    
    onConnected() {
      this.reconnectAttempts = 0 // 重置重连计数
    }
  }
}
```

## 注意事项

1. **网络要求**: 确保客户端能够访问数字人服务器地址
2. **浏览器兼容性**: 需要支持 WebRTC 的现代浏览器
3. **HTTPS**: 在生产环境中建议使用 HTTPS 协议
4. **性能优化**: 避免同时创建多个数字人实例
5. **内存管理**: 组件销毁时会自动断开连接，无需手动处理

## 故障排除

### 常见问题

1. **连接失败**: 检查服务器地址和网络连接
2. **无视频显示**: 检查 WebRTC 支持和防火墙设置
3. **音频问题**: 确保浏览器允许自动播放音频
4. **消息发送失败**: 检查会话状态和服务器响应

### 调试技巧

```javascript
// 开启详细日志
mounted() {
  // 监听所有事件进行调试
  this.$refs.digitalHuman.$on('connecting', () => console.log('开始连接'))
  this.$refs.digitalHuman.$on('connected', (data) => console.log('连接成功', data))
  this.$refs.digitalHuman.$on('disconnected', () => console.log('连接断开'))
  this.$refs.digitalHuman.$on('error', (error) => console.error('错误', error))
}
```
