<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue2 数字人组件集成示例</title>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .app-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        
        .demo-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fafafa;
        }
        
        .demo-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #555;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            align-items: start;
        }
        
        .api-demo {
            padding: 15px;
            background: white;
            border-radius: 6px;
            border: 1px solid #ddd;
        }
        
        .api-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .api-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }
        
        .api-btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .api-btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .api-btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        
        .api-btn:hover {
            opacity: 0.8;
            transform: translateY(-1px);
        }
        
        .api-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .log-area {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 150px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        .quick-messages {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }
        
        .quick-msg-btn {
            padding: 4px 8px;
            background-color: #e9ecef;
            border: 1px solid #ced4da;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }
        
        .quick-msg-btn:hover {
            background-color: #dee2e6;
        }
        
        @media (max-width: 768px) {
            .demo-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div id="app" class="app-container">
        <div class="header">
            <h1>Vue2 数字人组件集成示例</h1>
            <p>演示如何在Vue2项目中集成数字人组件</p>
        </div>
        
        <!-- 基础使用示例 -->
        <div class="demo-section">
            <div class="demo-title">基础使用示例</div>
            <div class="demo-grid">
                <digital-human
                    ref="basicDemo"
                    :server-url="serverUrl"
                    :width="400"
                    :height="300"
                    :show-controls="true"
                    @connected="onConnected"
                    @disconnected="onDisconnected"
                    @message-sent="onMessageSent"
                    @error="onError"
                ></digital-human>
                
                <div class="api-demo">
                    <h4>API 控制演示</h4>
                    <div class="api-buttons">
                        <button 
                            @click="connectBasic" 
                            :disabled="basicConnected"
                            class="api-btn api-btn-primary"
                        >
                            连接
                        </button>
                        <button 
                            @click="disconnectBasic" 
                            :disabled="!basicConnected"
                            class="api-btn api-btn-warning"
                        >
                            断开
                        </button>
                        <button 
                            @click="sendTestMessage" 
                            :disabled="!basicConnected"
                            class="api-btn api-btn-success"
                        >
                            发送测试消息
                        </button>
                    </div>
                    
                    <div class="quick-messages">
                        <span style="font-size: 12px; color: #666;">快速消息：</span>
                        <button 
                            v-for="msg in quickMessages" 
                            :key="msg"
                            @click="sendQuickMessage(msg)"
                            :disabled="!basicConnected"
                            class="quick-msg-btn"
                        >
                            {{ msg }}
                        </button>
                    </div>
                    
                    <div style="margin-top: 15px;">
                        <label style="font-size: 12px; color: #666;">事件日志：</label>
                        <div class="log-area">{{ eventLog }}</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 自动连接示例 -->
        <div class="demo-section">
            <div class="demo-title">自动连接 + 无控制面板示例</div>
            <div class="demo-grid">
                <digital-human
                    ref="autoDemo"
                    :server-url="serverUrl"
                    :width="350"
                    :height="250"
                    :show-controls="false"
                    :auto-connect="true"
                    default-message-type="echo"
                    @connected="onAutoConnected"
                    @disconnected="onAutoDisconnected"
                    @error="onAutoError"
                ></digital-human>
                
                <div class="api-demo">
                    <h4>纯API控制</h4>
                    <p style="font-size: 12px; color: #666; margin-bottom: 15px;">
                        此示例隐藏了控制面板，完全通过API控制
                    </p>
                    
                    <div class="api-buttons">
                        <button 
                            @click="sendEchoMessage('你好，我是数字人助手')"
                            :disabled="!autoConnected"
                            class="api-btn api-btn-success"
                        >
                            朗读问候语
                        </button>
                        <button 
                            @click="sendEchoMessage('欢迎使用数字人服务')"
                            :disabled="!autoConnected"
                            class="api-btn api-btn-success"
                        >
                            朗读欢迎语
                        </button>
                        <button 
                            @click="sendChatMessage('请介绍一下你自己')"
                            :disabled="!autoConnected"
                            class="api-btn api-btn-primary"
                        >
                            对话测试
                        </button>
                    </div>
                    
                    <div style="margin-top: 15px;">
                        <label style="font-size: 12px; color: #666;">自动连接状态：</label>
                        <div class="log-area">{{ autoLog }}</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 配置说明 -->
        <div class="demo-section">
            <div class="demo-title">组件配置说明</div>
            <div style="font-size: 14px; line-height: 1.6;">
                <h4>Props 参数：</h4>
                <ul>
                    <li><code>server-url</code>: 数字人服务器地址，默认 'http://************:8010'</li>
                    <li><code>show-controls</code>: 是否显示控制面板，默认 true</li>
                    <li><code>width/height</code>: 容器尺寸，支持数字或字符串</li>
                    <li><code>auto-connect</code>: 是否自动连接，默认 false</li>
                    <li><code>default-message-type</code>: 默认消息类型，'chat' 或 'echo'</li>
                </ul>
                
                <h4>事件：</h4>
                <ul>
                    <li><code>@connected</code>: 连接成功时触发</li>
                    <li><code>@disconnected</code>: 断开连接时触发</li>
                    <li><code>@message-sent</code>: 消息发送成功时触发</li>
                    <li><code>@error</code>: 发生错误时触发</li>
                </ul>
                
                <h4>方法：</h4>
                <ul>
                    <li><code>connect()</code>: 手动连接</li>
                    <li><code>disconnect()</code>: 手动断开</li>
                    <li><code>sendText(text, type)</code>: 发送指定文本</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 注册数字人组件（这里简化处理，实际项目中应该通过模块导入）
        // 在实际项目中，你需要将 DigitalHuman.vue 文件导入并注册
        
        new Vue({
            el: '#app',
            data: {
                serverUrl: 'http://************:8010',
                basicConnected: false,
                autoConnected: false,
                eventLog: '等待事件...\n',
                autoLog: '自动连接中...\n',
                quickMessages: ['你好', '介绍一下自己', '今天天气怎么样', '再见']
            },
            methods: {
                // 基础示例事件处理
                onConnected(data) {
                    this.basicConnected = true
                    this.addLog(`✅ 连接成功，会话ID: ${data.sessionId}`)
                },
                
                onDisconnected() {
                    this.basicConnected = false
                    this.addLog('❌ 连接已断开')
                },
                
                onMessageSent(data) {
                    this.addLog(`📤 消息已发送: ${data.text} (${data.type})`)
                },
                
                onError(error) {
                    this.addLog(`❌ 错误: ${error}`)
                },
                
                // 自动连接示例事件处理
                onAutoConnected(data) {
                    this.autoConnected = true
                    this.addAutoLog(`✅ 自动连接成功，会话ID: ${data.sessionId}`)
                },
                
                onAutoDisconnected() {
                    this.autoConnected = false
                    this.addAutoLog('❌ 自动连接已断开')
                },
                
                onAutoError(error) {
                    this.addAutoLog(`❌ 自动连接错误: ${error}`)
                },
                
                // API 控制方法
                connectBasic() {
                    this.$refs.basicDemo.connect()
                },
                
                disconnectBasic() {
                    this.$refs.basicDemo.disconnect()
                },
                
                sendTestMessage() {
                    this.$refs.basicDemo.sendText('这是一条测试消息', 'chat')
                        .then(() => {
                            this.addLog('📤 API发送测试消息成功')
                        })
                        .catch(error => {
                            this.addLog(`❌ API发送失败: ${error.message}`)
                        })
                },
                
                sendQuickMessage(message) {
                    this.$refs.basicDemo.sendText(message, 'chat')
                },
                
                sendEchoMessage(text) {
                    this.$refs.autoDemo.sendText(text, 'echo')
                        .then(() => {
                            this.addAutoLog(`📤 朗读消息: ${text}`)
                        })
                        .catch(error => {
                            this.addAutoLog(`❌ 朗读失败: ${error.message}`)
                        })
                },
                
                sendChatMessage(text) {
                    this.$refs.autoDemo.sendText(text, 'chat')
                        .then(() => {
                            this.addAutoLog(`📤 对话消息: ${text}`)
                        })
                        .catch(error => {
                            this.addAutoLog(`❌ 对话失败: ${error.message}`)
                        })
                },
                
                // 日志工具方法
                addLog(message) {
                    const timestamp = new Date().toLocaleTimeString()
                    this.eventLog += `[${timestamp}] ${message}\n`
                    this.$nextTick(() => {
                        const logArea = document.querySelector('.log-area')
                        if (logArea) logArea.scrollTop = logArea.scrollHeight
                    })
                },
                
                addAutoLog(message) {
                    const timestamp = new Date().toLocaleTimeString()
                    this.autoLog += `[${timestamp}] ${message}\n`
                    this.$nextTick(() => {
                        const logAreas = document.querySelectorAll('.log-area')
                        if (logAreas[1]) logAreas[1].scrollTop = logAreas[1].scrollHeight
                    })
                }
            }
        })
    </script>
</body>
</html>
