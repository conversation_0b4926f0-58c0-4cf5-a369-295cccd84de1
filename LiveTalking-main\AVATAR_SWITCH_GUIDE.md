# Avatar切换功能使用指南

## 功能概述

本功能允许在数字人程序运行时动态切换avatar形象，无需重启整个程序。支持通过API接口和前端界面两种方式进行切换。

## 新增API接口

### 1. 切换Avatar接口

**接口地址**: `POST /switch_avatar`

**请求参数**:
```json
{
    "sessionid": 0,           // 会话ID
    "avatar_id": "avatar_name" // 要切换到的avatar ID
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "avatar switched successfully",
    "avatar_id": "avatar_name"
}
```

### 2. 获取Avatar列表接口

**接口地址**: `GET /avatar_list`

**响应示例**:
```json
{
    "code": 0,
    "data": [
        {
            "avatar_id": "wav2lip256_avatar1",
            "name": "wav2lip256_avatar1",
            "path": "./data/avatars/wav2lip256_avatar1"
        },
        {
            "avatar_id": "wav2lip256_avatar2", 
            "name": "wav2lip256_avatar2",
            "path": "./data/avatars/wav2lip256_avatar2"
        }
    ]
}
```

## 前端界面使用

### 1. 启动前端项目

```bash
cd LiveTalking-main/web/dh-frontend
npm install
npm run dev
```

### 2. 使用Avatar切换功能

1. 打开前端界面后，点击右下角的"设置"按钮
2. 在设置面板中找到"Avatar形象切换"部分
3. 点击"刷新列表"按钮加载可用的avatar
4. 在avatar网格中点击想要切换的avatar卡片
5. 系统会自动切换到选中的avatar（需要先连接数字人）

### 3. 功能特点

- **实时切换**: 无需重启程序，即时生效
- **状态显示**: 当前使用的avatar会有"当前"标识
- **错误处理**: 切换失败时会显示错误信息
- **权限检查**: 只有在连接状态下才能切换avatar

## 测试方法

### 1. 使用Python测试脚本

```bash
cd LiveTalking-main
python test_avatar_switch.py
```

### 2. 使用curl命令测试

```bash
# 获取avatar列表
curl -X GET http://localhost:8010/avatar_list

# 切换avatar
curl -X POST http://localhost:8010/switch_avatar \
  -H "Content-Type: application/json" \
  -d '{"sessionid": 0, "avatar_id": "wav2lip256_avatar2"}'
```

### 3. 使用浏览器测试

访问 `http://localhost:8010/avatar_switch_demo.html` 查看独立的测试页面。

## 支持的模型

当前支持以下模型的avatar切换：

- **wav2lip**: 支持完整的avatar切换功能
- **musetalk**: 支持完整的avatar切换功能  
- **ultralight**: 支持完整的avatar切换功能

## 注意事项

1. **Avatar文件结构**: 确保avatar目录包含必要的文件：
   - wav2lip: `full_imgs/`, `face_imgs/`, `coords.pkl`
   - musetalk: `full_imgs/`, `coords.pkl`, `latents.pt`
   - ultralight: `full_imgs/`, `face_imgs/`, `coords.pkl`

2. **会话状态**: 只有在已连接的会话中才能切换avatar

3. **性能考虑**: 切换avatar时会有短暂的处理时间，建议在数字人空闲时进行切换

4. **错误处理**: 如果切换失败，系统会保持当前avatar不变

## 故障排除

### 常见问题

1. **"session not found"错误**
   - 确保sessionid正确
   - 检查数字人是否已正确连接

2. **"avatar not found"错误**
   - 检查avatar目录是否存在
   - 确认avatar文件结构完整

3. **切换无响应**
   - 检查网络连接
   - 查看服务器日志获取详细错误信息

### 日志查看

服务器端日志会记录avatar切换的详细信息：

```bash
tail -f LiveTalking-main/livetalking.log
```

## 开发说明

### 代码结构

- `app.py`: 新增API接口实现
- `basereal.py`: 基础avatar切换方法
- `lipreal.py`, `musereal.py`, `lightreal.py`: 各模型的具体实现
- `web/dh-frontend/`: 前端界面集成

### 扩展开发

如需添加新的模型支持，需要：

1. 在对应的模型文件中实现`switch_avatar`方法
2. 在`app.py`的`switch_avatar_for_session`函数中添加新模型的处理逻辑
3. 确保新模型的`load_avatar`函数返回正确的数据格式

## 更新日志

- **v1.0**: 初始版本，支持基础avatar切换功能
- 支持wav2lip、musetalk、ultralight三种模型
- 提供完整的前端界面集成
- 包含测试工具和文档
