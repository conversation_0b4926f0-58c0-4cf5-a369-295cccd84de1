#!/usr/bin/env python3
"""
创建基于单张图像的数字人avatar
将单张图像复制多份作为视频帧序列，实现静态默认形象
"""

import os
import shutil
import argparse
import cv2
import glob
from musetalk.simple_musetalk import main_musetalk

def create_image_avatar(image_path, avatar_id, num_frames=30):
    """
    基于单张图像创建数字人avatar
    
    Args:
        image_path: 输入图像路径
        avatar_id: avatar ID
        num_frames: 生成的帧数（用于循环播放）
    """
    
    # 创建临时目录
    temp_dir = f"./temp_avatar_{avatar_id}"
    os.makedirs(temp_dir, exist_ok=True)
    
    try:
        # 读取原始图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        print(f"正在处理图像: {image_path}")
        print(f"图像尺寸: {image.shape}")
        
        # 将单张图像复制为多帧
        for i in range(num_frames):
            output_path = f"{temp_dir}/{i:08d}.png"
            cv2.imwrite(output_path, image)
        
        print(f"已生成 {num_frames} 帧图像到临时目录")
        
        # 调用musetalk处理流程
        print("开始处理avatar数据...")
        main_musetalk(temp_dir, avatar_id)
        
        print(f"Avatar '{avatar_id}' 创建完成!")
        print(f"数据保存在: ./data/avatars/{avatar_id}")
        
    finally:
        # 清理临时目录
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
            print("临时目录已清理")

def main():
    parser = argparse.ArgumentParser(description='创建基于单张图像的数字人avatar')
    parser.add_argument('--image', type=str, required=True, help='输入图像路径')
    parser.add_argument('--avatar_id', type=str, required=True, help='Avatar ID')
    parser.add_argument('--frames', type=int, default=30, help='生成的帧数 (默认: 30)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.image):
        print(f"错误: 图像文件不存在: {args.image}")
        return
    
    create_image_avatar(args.image, args.avatar_id, args.frames)

if __name__ == "__main__":
    main()
