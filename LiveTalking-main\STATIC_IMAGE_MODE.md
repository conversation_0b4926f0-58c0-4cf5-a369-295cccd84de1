# 静态图像模式使用指南

本文档介绍如何将LiveTalking的默认形象从视频改为静态图像。

## 概述

默认情况下，LiveTalking使用视频帧序列作为数字人的默认形象。在没有语音输入时，系统会循环播放这些视频帧。通过静态图像模式，可以将默认形象改为单张静态图像，减少资源消耗并提供更稳定的视觉效果。

## 使用方法

### 方法1：使用静态图像模式启动（推荐）

1. **准备静态图像**
   ```bash
   # 确保图像文件存在且格式正确（支持 jpg, png 等）
   ls your_image.jpg
   ```

2. **使用启动脚本**
   ```bash
   python run_with_static_image.py --image your_image.jpg --model musetalk
   ```

3. **手动启动**
   ```bash
   python app.py --static_image_mode --static_image_path your_image.jpg --model musetalk --avatar_id avator_1
   ```

### 方法2：创建基于图像的Avatar

1. **使用图像创建Avatar**
   ```bash
   python create_image_avatar.py --image your_image.jpg --avatar_id my_static_avatar --frames 30
   ```

2. **启动服务**
   ```bash
   python app.py --model musetalk --avatar_id my_static_avatar
   ```

### 方法3：使用自定义配置

1. **编辑配置文件**
   ```bash
   # 编辑 data/static_image_config.json
   # 或创建自定义配置文件
   ```

2. **启动时指定配置**
   ```bash
   python app.py --customvideo_config data/static_image_config.json --model musetalk
   ```

## 配置参数

### 命令行参数

- `--static_image_mode`: 启用静态图像模式
- `--static_image_path`: 静态图像文件路径
- `--model`: 使用的模型 (musetalk, wav2lip, ultralight)
- `--avatar_id`: Avatar ID

### 配置文件格式

```json
[
    {
        "audiotype": 1,
        "imgpath": "./data/static_images/default.png",
        "audiopath": "./data/audio/silence.wav",
        "description": "默认静态图像形象"
    }
]
```

## 技术原理

### 默认形象显示逻辑

```python
if audio_frames[0][1]!=0 and audio_frames[1][1]!=0: # 静音状态
    self.speaking = False
    if self.static_image_mode and self.static_image_frame is not None:
        # 使用静态图像
        target_frame = self.static_image_frame.copy()
    else:
        # 使用视频帧序列
        target_frame = self.frame_list_cycle[idx]
```

### 优势

1. **资源节约**: 减少内存和计算资源消耗
2. **稳定性**: 避免视频帧切换带来的闪烁
3. **简单性**: 只需一张图像即可创建数字人
4. **兼容性**: 与现有TTS和ASR系统完全兼容

## 注意事项

1. **图像质量**: 建议使用高质量、清晰的人脸图像
2. **图像尺寸**: 建议使用标准尺寸（如512x512）以获得最佳效果
3. **人脸检测**: 确保图像中包含清晰可识别的人脸
4. **格式支持**: 支持常见图像格式（jpg, png, bmp等）

## 故障排除

### 常见问题

1. **图像无法加载**
   - 检查文件路径是否正确
   - 确认图像格式是否支持
   - 检查文件权限

2. **人脸检测失败**
   - 确保图像中包含清晰的人脸
   - 尝试调整图像亮度和对比度
   - 使用正面人脸图像

3. **性能问题**
   - 调整图像尺寸
   - 检查系统资源使用情况

### 日志信息

启动时会显示相关日志：
```
已加载静态图像: your_image.jpg
静态图像模式已启用
```

## 示例

### 基本使用
```bash
# 使用静态图像启动
python run_with_static_image.py --image portrait.jpg

# 访问 http://localhost:8010/dashboard.html
```

### 高级配置
```bash
# 指定模型和端口
python app.py \
  --static_image_mode \
  --static_image_path portrait.jpg \
  --model musetalk \
  --tts edgetts \
  --listenport 8010
```
