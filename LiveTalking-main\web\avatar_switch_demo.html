<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Avatar切换演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #555;
        }
        .avatar-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .avatar-item {
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s;
        }
        .avatar-item:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .avatar-item.active {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎭 Avatar切换演示</h1>
        
        <div class="section">
            <h3>📋 会话设置</h3>
            <div class="input-group">
                <label for="sessionId">会话ID:</label>
                <input type="number" id="sessionId" value="0" min="0">
            </div>
            <button onclick="loadAvatarList()">🔄 刷新Avatar列表</button>
            <button onclick="checkSpeakingStatus()">🎤 检查说话状态</button>
        </div>

        <div class="section">
            <h3>🎨 可用Avatar列表</h3>
            <div id="avatarList" class="avatar-list">
                <div class="status info">点击"刷新Avatar列表"加载可用的Avatar</div>
            </div>
        </div>

        <div class="section">
            <h3>📊 状态信息</h3>
            <div id="statusArea">
                <div class="status info">准备就绪，请先加载Avatar列表</div>
            </div>
        </div>
    </div>

    <script>
        let currentSessionId = 0;
        let currentAvatarId = null;
        let avatarList = [];

        // 显示状态消息
        function showStatus(message, type = 'info') {
            const statusArea = document.getElementById('statusArea');
            statusArea.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // 获取会话ID
        function getSessionId() {
            return parseInt(document.getElementById('sessionId').value) || 0;
        }

        // 加载Avatar列表
        async function loadAvatarList() {
            showStatus('正在加载Avatar列表...', 'info');
            
            try {
                const response = await fetch('/avatar_list');
                const data = await response.json();
                
                if (data.code === 0) {
                    avatarList = data.data;
                    displayAvatarList(avatarList);
                    showStatus(`成功加载 ${avatarList.length} 个Avatar`, 'success');
                } else {
                    showStatus(`加载Avatar列表失败: ${data.msg}`, 'error');
                }
            } catch (error) {
                showStatus(`请求失败: ${error.message}`, 'error');
            }
        }

        // 显示Avatar列表
        function displayAvatarList(avatars) {
            const avatarListDiv = document.getElementById('avatarList');
            
            if (avatars.length === 0) {
                avatarListDiv.innerHTML = '<div class="status info">没有找到可用的Avatar</div>';
                return;
            }

            avatarListDiv.innerHTML = avatars.map(avatar => `
                <div class="avatar-item" onclick="switchAvatar('${avatar.avatar_id}')" 
                     data-avatar-id="${avatar.avatar_id}">
                    <strong>${avatar.name}</strong><br>
                    <small>ID: ${avatar.avatar_id}</small>
                </div>
            `).join('');
        }

        // 切换Avatar
        async function switchAvatar(avatarId) {
            const sessionId = getSessionId();
            showStatus(`正在切换到Avatar: ${avatarId}...`, 'info');
            
            try {
                const response = await fetch('/switch_avatar', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        sessionid: sessionId,
                        avatar_id: avatarId
                    })
                });
                
                const data = await response.json();
                
                if (data.code === 0) {
                    currentAvatarId = avatarId;
                    updateAvatarSelection(avatarId);
                    showStatus(`成功切换到Avatar: ${data.avatar_id}`, 'success');
                } else {
                    showStatus(`切换Avatar失败: ${data.msg}`, 'error');
                }
            } catch (error) {
                showStatus(`请求失败: ${error.message}`, 'error');
            }
        }

        // 更新Avatar选择状态
        function updateAvatarSelection(selectedAvatarId) {
            const avatarItems = document.querySelectorAll('.avatar-item');
            avatarItems.forEach(item => {
                if (item.dataset.avatarId === selectedAvatarId) {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            });
        }

        // 检查说话状态
        async function checkSpeakingStatus() {
            const sessionId = getSessionId();
            showStatus('正在检查说话状态...', 'info');
            
            try {
                const response = await fetch('/is_speaking', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        sessionid: sessionId
                    })
                });
                
                const data = await response.json();
                
                if (data.code === 0) {
                    const speaking = data.data;
                    showStatus(`会话 ${sessionId} 状态: ${speaking ? '正在说话' : '未在说话'}`, 'success');
                } else {
                    showStatus(`检查状态失败: ${data.msg}`, 'error');
                }
            } catch (error) {
                showStatus(`请求失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后自动加载Avatar列表
        window.addEventListener('load', function() {
            loadAvatarList();
        });
    </script>
</body>
</html>
