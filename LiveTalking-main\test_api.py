#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API接口的简单脚本
"""

import requests
import json

def test_avatar_list_api():
    """测试avatar列表API"""
    print("测试avatar列表API...")
    
    try:
        # 测试不同的URL格式
        urls = [
            "http://localhost:8010/avatar_list",
            "http://127.0.0.1:8010/avatar_list"
        ]
        
        for url in urls:
            print(f"\n尝试请求: {url}")
            try:
                response = requests.get(url, timeout=5)
                print(f"状态码: {response.status_code}")
                print(f"响应头: {dict(response.headers)}")
                print(f"响应内容: {response.text[:200]}...")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"JSON解析成功: {data}")
                        return True
                    except json.JSONDecodeError as e:
                        print(f"JSON解析失败: {e}")
                        
            except requests.exceptions.RequestException as e:
                print(f"请求失败: {e}")
                
    except Exception as e:
        print(f"测试异常: {e}")
        
    return False

def test_server_status():
    """测试服务器状态"""
    print("测试服务器基本状态...")
    
    try:
        # 测试根路径
        response = requests.get("http://localhost:8010/", timeout=5)
        print(f"根路径状态码: {response.status_code}")
        print(f"根路径响应类型: {response.headers.get('content-type', 'unknown')}")
        
        # 测试一个已知的API
        response = requests.post("http://localhost:8010/is_speaking", 
                               json={"sessionid": 0}, 
                               timeout=5)
        print(f"is_speaking API状态码: {response.status_code}")
        print(f"is_speaking API响应: {response.text}")
        
    except Exception as e:
        print(f"服务器状态测试失败: {e}")

if __name__ == "__main__":
    print("开始API测试...")
    print("=" * 50)
    
    test_server_status()
    print("=" * 50)
    test_avatar_list_api()
    
    print("=" * 50)
    print("测试完成")
